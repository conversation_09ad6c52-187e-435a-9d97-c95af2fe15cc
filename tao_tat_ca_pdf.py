#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để tạo tất cả PDF từ Excel
"""

try:
    from tao_pdf_theo_mau import PDFGenerator
    
    print("Bắt đầu tạo TẤT CẢ PDF...")
    print(f"File Excel: Danh muc Import.V3.xlsx")
    print(f"Thư mục output: output_pdfs")
    
    # Khởi tạo generator
    generator = PDFGenerator("Danh muc Import.V3.xlsx", "output_pdfs")
    
    print(f"Sẽ tạo {len(generator.data)} file PDF...")
    
    # Xác nhận từ người dùng
    confirm = input("Bạn có muốn tiếp tục? (y/n): ")
    
    if confirm.lower() in ['y', 'yes', 'có']:
        # Tạo tất cả PDF
        generator.create_all_pdfs()
        print("Hoàn thành tạo tất cả PDF! Ki<PERSON>m tra thư mục output_pdfs để xem kết quả.")
    else:
        print("Đ<PERSON> hủy.")
    
except Exception as e:
    print(f"Lỗi: {e}")
    import traceback
    traceback.print_exc()
