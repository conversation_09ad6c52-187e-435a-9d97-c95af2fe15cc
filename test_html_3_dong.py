#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script test để tạo HTML cho 3 dòng đầu tiên
"""

try:
    from tao_html_tu_mau import HTMLGenerator
    
    print("Bắt đầu tạo HTML từ template...")
    print(f"File Excel: import1.xlsx")
    print(f"HTML Template: template_simple.html")
    print(f"Thư mục output: output_html")
    
    # Khởi tạo generator
    generator = HTMLGenerator("import1.xlsx", "template_simple.html", "output_html")
    
    # Tạo HTML cho 3 dòng đầu tiên
    print("Tạo HTML cho 3 dòng đầu tiên để test...")
    for index, row in generator.data.head(3).iterrows():
        generator.create_single_html(row, index)
    
    print("Hoàn thành test! Kiểm tra thư mục output_html để xem kết quả.")
    print("Bạn có thể mở các file HTML trong trình duyệt để xem.")
    
except Exception as e:
    print(f"Lỗi: {e}")
    import traceback
    traceback.print_exc()
