#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script test đơn giản để debug
"""

import pandas as pd
import qrcode
import base64
import io

def generate_qr_code_base64(data):
    """Tạo QR code và trả về base64"""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = io.BytesIO()
        qr_img.save(buffer, format='PNG')
        qr_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{qr_base64}"
    except Exception as e:
        print(f"Lỗi tạo QR: {e}")
        return ""

def main():
    try:
        print("Đọc dữ liệu từ import1.xlsx...")
        
        # Đọc file Excel
        df = pd.read_excel('import1.xlsx')
        print(f"Đã đọc {len(df)} dòng dữ liệu")
        print("Cột:", df.columns.tolist())
        
        # Lấy dòng đầu tiên
        first_row = df.iloc[0]
        print("\nDữ liệu dòng đầu tiên:")
        for col in df.columns:
            print(f"  {col}: {first_row[col]}")
        
        # Đọc template HTML
        print("\nĐọc template HTML...")
        with open('template_simple.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Thay thế dữ liệu
        print("Thay thế dữ liệu...")
        qr_code = generate_qr_code_base64(str(first_row['Mã hồ sơ']))
        
        replacements = {
            '{{NAM}}': str(first_row['Năm']),
            '{{MA_HO_SO}}': str(first_row['Mã hồ sơ']),
            '{{THOI_GIAN_LUU}}': str(first_row['Thời gian lưu']),
            '{{VI_TRI}}': str(first_row['Vị trí']),
            '{{TEN_CHUNG_TU}}': str(first_row['Tên chứng từ']),
            '{{QR_CODE}}': qr_code
        }
        
        for placeholder, value in replacements.items():
            html_content = html_content.replace(placeholder, value)
            print(f"  Thay thế {placeholder} -> {value[:50]}...")
        
        # Lưu HTML test
        output_html = 'test_output.html'
        with open(output_html, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\nĐã tạo file HTML test: {output_html}")
        print("Hãy mở file này trong trình duyệt để kiểm tra!")
        
    except Exception as e:
        print(f"Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
