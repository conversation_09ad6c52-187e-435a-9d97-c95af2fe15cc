# HƯỚNG DẪN SỬ DỤNG CÔNG CỤ TẠO PDF

## 📋 Mô tả
Công cụ này sẽ đọc dữ liệu từ file Excel và tạo ra các file PDF theo mẫu thiết kế, mỗi PDF tương ứng với một dòng dữ liệu trong Excel.

## 🎯 Kết quả
Mỗi PDF sẽ có:
- **Tiêu đề**: PHÒNG KẾ TOÁN MEDLATEC
- **Logo**: Hình tròn vàng với chữ MEDLATEC
- **Thông tin hồ sơ**: Tên chứng từ và năm
- **Mã QR**: Chứa mã hồ sơ
- **Vị trí**: Hiển thị ở góc dưới trái
- **Thời hạn lưu và mã hồ sơ**: Hiển thị ở giữa

## 🚀 Cách sử dụng

### Bước 1: Cài đặt thư viện
```bash
pip install -r requirements.txt
```

### Bước 2: Chạy test với 3 file đầu tiên
```bash
python test_tao_pdf.py
```

### Bước 3: Chạy tạo tất cả PDF (522 file)
```bash
python tao_tat_ca_pdf.py
```

## 📁 Cấu trúc file

```
📁 Thư mục dự án/
├── 📄 Danh muc Import.V3.xlsx    # File Excel dữ liệu đầu vào (522 dòng)
├── 📄 tao_pdf_theo_mau.py       # Script chính tạo PDF
├── 📄 test_tao_pdf.py           # Script test với 3 file đầu tiên
├── 📄 tao_tat_ca_pdf.py         # Script tạo tất cả PDF
├── 📄 requirements.txt          # Danh sách thư viện cần thiết
└── 📁 output_pdfs/              # Thư mục chứa file PDF đã tạo
```

## 📊 Dữ liệu Excel

File Excel có cấu trúc:
- **Cột 1**: Vị trí (E324-A-1-1-1, E324-A-1-1-2, ...)
- **Cột 2**: Tên chứng từ (Chứng từ thu chi, Hồ sơ giảm trừ gia cảnh, ...)
- **Cột 3**: Năm (2007, 2009, 2010, ...)
- **Cột 4**: Mã hồ sơ (KTO2007-1, KTO2009-1, ...)
- **Cột 5**: Thời gian lưu (không giới hạn, ...)

**Tổng cộng**: 522 dòng dữ liệu

## 🎨 Thiết kế PDF

Mỗi PDF được tạo theo mẫu:
- **Kích thước**: A4
- **Màu chủ đạo**: Xanh dương (#2E3192)
- **Font**: Helvetica
- **Khung viền**: Màu xanh dương
- **QR Code**: 25mm x 25mm, chứa mã hồ sơ

## 📝 Tên file output

File PDF được đặt tên theo format:
```
ho_so_{số_thứ_tự}_{mã_hồ_sơ}.pdf
```

Ví dụ:
- `ho_so_1_KTO2007-1.pdf`
- `ho_so_2_KTO2009-1.pdf`
- `ho_so_3_KTO2010-1.pdf`

## ⚡ Hiệu suất

- **Test 3 file**: ~1-2 giây
- **Tất cả 522 file**: ~2-3 phút
- **Dung lượng**: Mỗi PDF ~50-100KB

## 🔧 Xử lý lỗi

### Lỗi thiếu thư viện
```bash
pip install pandas reportlab qrcode[pil] Pillow openpyxl
```

### Lỗi không đọc được Excel
- Kiểm tra file Excel có tồn tại không
- Đảm bảo file không bị khóa

### Lỗi tạo QR code
- Script sẽ tự động fallback thành text nếu không tạo được QR

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra log trong terminal
2. Đảm bảo tất cả thư viện đã được cài đặt
3. Kiểm tra quyền ghi vào thư mục output_pdfs

## ✅ Kết quả mong đợi

Sau khi chạy thành công, bạn sẽ có:
- 522 file PDF trong thư mục `output_pdfs/`
- Mỗi file PDF có thiết kế giống như mẫu
- QR code chứa mã hồ sơ tương ứng
- Tất cả thông tin được thay thế chính xác từ Excel
