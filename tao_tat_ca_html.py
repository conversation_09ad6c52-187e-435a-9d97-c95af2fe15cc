#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để tạo tất cả HTML từ Nhan-hop_3.html
"""

try:
    from clone_nhan_hop import NhanHopCloner
    
    print("Bắt đầu clone tất cả HTML từ Nhan-hop_3.html...")
    print(f"File Excel: import1.xlsx")
    print(f"HTML Template: Nhan-hop_3.html")
    print(f"Thư mục output: output_html")
    
    # Khởi tạo cloner
    cloner = <PERSON>hanHopCloner("import1.xlsx", "Nhan-hop_3.html", "output_html")
    
    # Tạo tất cả file HTML
    cloner.create_all_html()
    
    print("Hoàn thành! Tất cả file HTML đã được tạo trong thư mục output_html.")
    
except Exception as e:
    print(f"Lỗi: {e}")
    import traceback
    traceback.print_exc()
