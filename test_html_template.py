#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script test để tạo PDF từ HTML template cho 3 dòng đầu tiên
"""

try:
    from tao_pdf_theo_mau import PDFGenerator
    
    print("Bắt đầu tạo PDF từ HTML template...")
    print(f"File Excel: Danh muc Import.V3.xlsx")
    print(f"HTML Template: template_simple.html")
    print(f"Thư mục output: output_pdfs")
    
    # Khởi tạo generator với HTML template đơn giản
    generator = PDFGenerator("Danh muc Import.V3.xlsx", "template_simple.html", "output_pdfs")
    
    # Tạo PDF cho 3 dòng đầu tiên
    print("Tạo PDF cho 3 dòng đầu tiên để test...")
    for index, row in generator.data.head(3).iterrows():
        generator.create_single_pdf(row, index)
    
    print("Hoàn thành test! <PERSON><PERSON><PERSON> tra thư mục output_pdfs để xem kết quả.")
    
except Exception as e:
    print(f"Lỗi: {e}")
    import traceback
    traceback.print_exc()
