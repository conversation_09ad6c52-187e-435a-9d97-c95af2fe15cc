#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import qrcode
from PIL import Image
import io
import os
from pathlib import Path
import logging
import base64
from bs4 import BeautifulSoup
import re
from weasyprint import HTML

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFGenerator:
    def __init__(self, excel_file, html_template="Nhan-hop_3.html", output_folder="output_pdfs"):
        """
        Khởi tạo PDFGenerator

        Args:
            excel_file (str): Đường dẫn đến file Excel chứa dữ liệu
            html_template (str): Đường dẫn đến file HTML template
            output_folder (str): Th<PERSON> mục lưu file PDF đã tạo
        """
        self.excel_file = excel_file
        self.html_template = html_template
        self.output_folder = output_folder

        # Tạ<PERSON> thư mục output nếu chưa tồn tại
        Path(self.output_folder).mkdir(exist_ok=True)

        # Đọc dữ liệu từ Excel
        self.data = self.load_excel_data()

        # Đọc HTML template
        self.html_content = self.load_html_template()
        
    def load_excel_data(self):
        """Đọc dữ liệu từ file Excel"""
        try:
            # Đọc file Excel, bỏ qua dòng đầu tiên (header)
            df = pd.read_excel(self.excel_file, skiprows=1)
            
            # Đặt tên cột cho dễ hiểu
            df.columns = ['vi_tri', 'ten_chung_tu', 'nam', 'ma_ho_so', 'thoi_gian_luu']
            
            # Loại bỏ các dòng trống
            df = df.dropna(subset=['vi_tri', 'ma_ho_so'])
            
            logger.info(f"Đã đọc {len(df)} dòng dữ liệu từ {self.excel_file}")
            return df
        except Exception as e:
            logger.error(f"Lỗi khi đọc file Excel: {e}")
            raise
    
    def load_html_template(self):
        """Đọc file HTML template"""
        try:
            with open(self.html_template, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Đã đọc HTML template từ {self.html_template}")
            return content
        except Exception as e:
            logger.error(f"Lỗi khi đọc HTML template: {e}")
            raise
        
    def generate_qr_code_base64(self, data):
        """
        Tạo mã QR và trả về dưới dạng base64

        Args:
            data (str): Dữ liệu để tạo QR code

        Returns:
            str: QR code dưới dạng base64 string
        """
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64
            buffer = io.BytesIO()
            qr_img.save(buffer, format='PNG')
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{qr_base64}"
        except Exception as e:
            logger.warning(f"Không thể tạo QR code: {e}")
            return ""
    
    def create_html_from_template(self, row_data):
        """
        Tạo HTML từ template với dữ liệu thực tế

        Args:
            row_data: Dữ liệu của một dòng từ Excel

        Returns:
            str: HTML content đã được thay thế dữ liệu
        """
        html_content = self.html_content

        # Thay thế các placeholder trong HTML
        # Tạo QR code
        qr_data = row_data.get('ma_ho_so', '')
        qr_base64 = self.generate_qr_code_base64(qr_data) if qr_data else ""

        # Các thông tin cần thay thế
        replacements = {
            '{{NAM}}': str(row_data.get('nam', '')),
            '{{MA_HO_SO}}': str(row_data.get('ma_ho_so', '')),
            '{{THOI_GIAN_LUU}}': str(row_data.get('thoi_gian_luu', '')),
            '{{VI_TRI}}': str(row_data.get('vi_tri', '')),
            '{{TEN_CHUNG_TU}}': str(row_data.get('ten_chung_tu', '')),
            '{{QR_CODE}}': qr_base64
        }

        # Thực hiện thay thế
        for placeholder, value in replacements.items():
            html_content = html_content.replace(placeholder, value)

        return html_content
        
    def create_single_pdf(self, row_data, row_index):
        """
        Tạo một file PDF cho một dòng dữ liệu từ HTML template

        Args:
            row_data: Dữ liệu của một dòng từ Excel
            row_index (int): Chỉ số dòng
        """
        try:
            # Tạo tên file output
            ma_ho_so = row_data.get('ma_ho_so', f'Row_{row_index}')
            output_filename = f"ho_so_{row_index + 1}_{ma_ho_so}.pdf"
            output_path = os.path.join(self.output_folder, output_filename)

            # Tạo HTML từ template
            html_content = self.create_html_from_template(row_data)

            # Tạo file HTML tạm thời
            temp_html_path = f"temp_{row_index + 1}.html"
            with open(temp_html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # Convert HTML sang PDF bằng weasyprint
            try:
                HTML(filename=temp_html_path).write_pdf(output_path)
                logger.info(f"Đã tạo thành công file: {output_filename}")

            except Exception as e:
                logger.error(f"Lỗi khi convert HTML sang PDF: {e}")
                # Fallback: lưu file HTML thay vì PDF
                fallback_path = os.path.join(self.output_folder, f"ho_so_{row_index + 1}_{ma_ho_so}.html")
                os.rename(temp_html_path, fallback_path)
                logger.info(f"Đã lưu file HTML thay thế: {fallback_path}")
                return

            # Xóa file HTML tạm thời
            if os.path.exists(temp_html_path):
                os.remove(temp_html_path)

        except Exception as e:
            logger.error(f"Lỗi khi tạo PDF cho dòng {row_index + 1}: {e}")
    
    def create_all_pdfs(self):
        """Tạo tất cả các file PDF"""
        logger.info("Bắt đầu tạo tất cả các file PDF...")
        
        for index, row in self.data.iterrows():
            self.create_single_pdf(row, index)
        
        logger.info(f"Hoàn thành tạo {len(self.data)} file PDF")

def main():
    """Hàm main để chạy chương trình"""
    try:
        # Khởi tạo generator với HTML template
        generator = PDFGenerator("Danh muc Import.V3.xlsx", "Nhan-hop_3.html", "output_pdfs")

        # Tạo tất cả file PDF
        generator.create_all_pdfs()

        print(f"Tạo PDF hoàn tất! Kiểm tra thư mục output_pdfs để xem kết quả.")

    except Exception as e:
        logger.error(f"Lỗi trong quá trình tạo PDF: {e}")
        print(f"Có lỗi xảy ra: {e}")

if __name__ == "__main__":
    main()
