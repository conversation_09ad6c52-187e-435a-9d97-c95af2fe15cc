<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .label-container {
            width: 180mm;
            height: 120mm;
            background-color: white;
            border: 3px solid #000;
            margin: 0 auto;
            position: relative;
            padding: 10mm;
            box-sizing: border-box;
        }
        
        .logo {
            position: absolute;
            top: 15mm;
            left: 15mm;
            width: 24mm;
            height: 24mm;
            background-color: #FFD700;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            color: #2E3192;
        }
        
        .title {
            text-align: center;
            color: #2E3192;
            font-size: 14px;
            font-weight: bold;
            margin-top: 5mm;
            margin-bottom: 5mm;
        }
        
        .divider {
            border-bottom: 1px solid #2E3192;
            margin: 5mm 10mm;
        }
        
        .subtitle {
            text-align: center;
            color: #2E3192;
            font-size: 12px;
            font-weight: bold;
            margin: 10mm 0;
        }
        
        .content-area {
            display: flex;
            margin-top: 15mm;
        }
        
        .qr-section {
            width: 30mm;
            text-align: center;
        }
        
        .qr-code {
            width: 20mm;
            height: 20mm;
            border: 1px solid #ccc;
            margin: 0 auto;
        }
        
        .info-section {
            flex: 1;
            margin-left: 10mm;
            color: #2E3192;
            font-size: 10px;
        }
        
        .info-item {
            margin-bottom: 5mm;
        }
        
        .position {
            position: absolute;
            bottom: 5mm;
            left: 5mm;
            color: #2E3192;
            font-size: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="label-container">
        <div class="logo">MEDLATEC</div>
        
        <div class="title">PHÒNG KẾ TOÁN MEDLATEC</div>
        
        <div class="divider"></div>
        
        <div class="subtitle">HỒ SƠ KẾ TOÁN NĂM {{NAM}}</div>
        
        <div class="content-area">
            <div class="qr-section">
                <img src="{{QR_CODE}}" alt="QR Code" class="qr-code" style="width: 20mm; height: 20mm;">
            </div>
            
            <div class="info-section">
                <div class="info-item">
                    <strong>Thời hạn lưu:</strong> {{THOI_GIAN_LUU}}
                </div>
                <div class="info-item">
                    <strong>Mã hồ sơ:</strong> {{MA_HO_SO}}
                </div>
                <div class="info-item">
                    <strong>Tên chứng từ:</strong> {{TEN_CHUNG_TU}}
                </div>
            </div>
        </div>
        
        <div class="position">{{VI_TRI}}</div>
    </div>
</body>
</html>
